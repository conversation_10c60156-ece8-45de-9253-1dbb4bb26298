package com.sinoyd.parse.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.JsonUtil;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.parse.dto.DtoAiInstrumentConfig;
import com.sinoyd.parse.dto.DtoAiParseFileApp;
import com.sinoyd.parse.dto.DtoParseDocument;
import com.sinoyd.parse.enums.*;
import com.sinoyd.parse.repository.AiInstrumentConfigRepository;
import com.sinoyd.parse.repository.AiParseFileAppRepository;
import com.sinoyd.parse.repository.ParseDocumentRepository;
import com.sinoyd.parse.service.AiParseFileAppService;
import com.sinoyd.parse.vo.*;
import com.sinoyd.parse.websocket.AiInstrumentParseWSServer;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * AI仪器解析应用操作接口实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
@Slf4j
@Service
public class AiParseFileAppServiceImpl extends BaseJpaServiceImpl<DtoAiParseFileApp, String, AiParseFileAppRepository> implements AiParseFileAppService {

    @Value("${ai-parse.url:http://*************:19990/api/call-agent/2?stream=true}")
    private String ocrUrl;

    @Value("${ai-parse.default-prompt:角色：你是位资深的光学数据识别专家,任务：你需要识别图片内容并将内容按照指定的json格式输出,要求,1.禁止将中文转成英文,禁止将单位转成中文,2.数据中的*需要去掉,3.数据不需要单位,4.采样日期和采样时间不需要分开。指定的json格式示例如下{参数名称1:参数值1,参数名称2:参数值2},去掉不必要的换行符,生成的json文本要能支持直接转换为java中的JSONObject,回答不需要其余说明,只需要输出json文本。}")
    private String ocrPrompt;

    @Value("${fileProps.filePath}")
    private String filePath;

    private AiInstrumentConfigRepository aiInstrumentConfigRepository;

    private ParseDocumentRepository parseDocumentRepository;

    private RedisTemplate redisTemplate;


    @Override
    public void findByPage(PageBean<DtoAiParseFileApp> pb, BaseCriteria aiParseFileAppCriteria) {
        pb.setEntityName("DtoAiParseFileApp a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, aiParseFileAppCriteria);

        // 设置解析方式名称和解析状态名称
        List<DtoAiParseFileApp> appList = pb.getData();
        loadTransientFields(appList);
    }

    @Override
    @Transactional
    public DtoAiParseFileApp save(DtoAiParseFileApp entity) {
        //设置解析状态为未解析
        entity.setParseStatus(EnumAIParseStatus.UN_PARS.getValue());
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoAiParseFileApp update(DtoAiParseFileApp entity) {
        return super.update(entity);
    }

    @Override
    public DtoAiParseFileApp findOne(String key) {
        DtoAiParseFileApp fileApp = super.findOne(key);
        loadTransientFields(Collections.singletonList(fileApp));
        return fileApp;
    }

    @Override
    public void batchParse(AiParseBatchVO parseBatchVO) {
        if (StringUtil.isEmpty(parseBatchVO.getAppIds())) {
            return;
        }
        //查询所选的AI解析应用
        List<DtoAiParseFileApp> appList = repository.findAll(parseBatchVO.getAppIds());
        loadTransientFields(appList);
        //AI解析应用Map
        Map<String, DtoAiParseFileApp> appMap = appList.stream().collect(Collectors.toMap(DtoAiParseFileApp::getId, dto -> dto));
        //判断解析应用中是否存在未上传附件的应用，如果存在，则抛出异常提醒
        if (appList.stream().anyMatch(p -> StringUtil.isEmpty(p.getDocumentPath()))) {
            throw new BaseException("存在未上传附件的应用，请先上传附件再进行解析！");
        }
        //SessionId
        String sessionId = parseBatchVO.getSessionId();
        // 固定3线程并发执行
//        ExecutorService executor = java.util.concurrent.Executors.newFixedThreadPool(2);
//        List<CompletableFuture<Void>> tasks = new java.util.ArrayList<>();

        for (String appId : parseBatchVO.getAppIds()) {
            DtoAiParseFileApp app = appMap.get(appId);
            sendOcrRequestMsg(parseBatchVO, appId, new OcrRequestVO(ocrUrl, app.getPromptText(), app.getDocumentPath()));
//            CompletableFuture<Void> task = CompletableFuture.runAsync(() ->
//                    sendOcrRequestMsg(parseBatchVO, appId, new OcrRequestVO(ocrUrl, app.getPromptText(), app.getDocumentPath())), executor);
//            tasks.add(task);
        }
        //向WebSocket发布全部解析完成消息
        AiInstrumentParseWSServer.send(sessionId, EnumWebSocketType.AI_PARSE_REALTIME, "全部解析完成", EnumMessageType.JSON);
//        String allFinish = "";
//        try {
//            CompletableFuture.allOf(tasks.toArray(new CompletableFuture[0])).join();
//            //向WebSocket发布全部解析完成消息
//            AiParseStepVO stepVO = new AiParseStepVO("-1", "全部解析完成", true, null, "数据全部解析完成");
//            allFinish = JsonUtil.toJson(stepVO);
//            AiInstrumentParseWSServer.send(sessionId, EnumWebSocketType.AI_PARSE_REALTIME, allFinish, EnumMessageType.JSON);
//        } catch (Exception e) {
//            log.error("最总结果JSON转换：", e);
//        } finally {
//            //TODO:执行批量数据保存
//            batchSaveRedis();
//        }
    }

    private void batchSaveRedis() {

    }

    @Override
    public DtoAiParseFileApp findAttachPath(String id) {
        return repository.findOne(id);
    }

    /**
     * AI解析请求（向WebSocket发送步骤消息）
     *
     * @param parseBatchVO 222
     * @param appId        AI解析提示词
     * @param requestVO    AI解析图片文件路径
     * @return 是否请求成功
     */
    public Integer sendOcrRequestMsg(AiParseBatchVO parseBatchVO, String appId, OcrRequestVO requestVO) {
        //解析步骤
        AtomicInteger step = new AtomicInteger(0);
        //解析参数结果容器
        AiParseDataContainerVO container = new AiParseDataContainerVO();

        try (BufferedReader reader = doStreamRequest(requestVO)) {
            String line;

            while ((line = reader.readLine()) != null) {
                step.set(step.get() + 1);
                if (!line.trim().isEmpty() && !line.contains("id:")) {
                    String json = line.replace("data: ", "");
                    JSONObject dataJsObj = JSONObject.parseObject(json);
                    try {
                        Boolean done = dataJsObj.getBoolean("done");
                        Boolean stepName = dataJsObj.getBoolean("done");
                        if (done) {

                            JSONObject originObject = dataJsObj.getJSONObject("workflow_result");
                            container.setAiAnswer(originObject.toJSONString());
                            container.setAiParseAppId(appId);


                            //TODO:解析数据转换，存入redis中
                            List<AiParseParamDataVO> parseDataList = new ArrayList<>();
//                            List<AiParseParamDataVO> parseDataList = parseSaveRedis();

                            //数据应用解析完成
                            container.setDataList(parseDataList);
                            String parseDataJson = JsonUtil.toJson(container);
                            //如果当前解析的应用为当前选中应用，则需要发布解析步骤数据
                            if (appId.equals(parseBatchVO.getCurrentAppId())) {
//                                WSMessageVO messageVO = new WSMessageVO(parseBatchVO.getSessionId(), EnumWebSocketType.AI_PARSE_REALTIME,EnumMessageType.JSON, parseDataJson);
                                AiInstrumentParseWSServer.send(parseBatchVO.getSessionId(), EnumWebSocketType.AI_PARSE_REALTIME, parseDataJson, EnumMessageType.JSON);
                            }
                            //创建解析完成消息
                            AiParseStepVO stepVO = new AiParseStepVO(String.valueOf(step.get()), "AI解析完成", done, appId, "AI解析完成");
                            String finishStepJson = JsonUtil.toJson(stepVO);
                            AiInstrumentParseWSServer.send(parseBatchVO.getSessionId(), EnumWebSocketType.AI_PARSE_REALTIME, finishStepJson, EnumMessageType.JSON);
                        } else {
                            //如果当前解析的应用为当前选中应用，则需要发布解析步骤数据
                            if (appId.equals(parseBatchVO.getCurrentAppId())) {
                                AiInstrumentParseWSServer.send(parseBatchVO.getSessionId(), EnumWebSocketType.AI_PARSE_REALTIME, json, EnumMessageType.JSON);
                            }
                        }
                        //TODO:将AI步骤数据保存到解析日志中（先存入缓存，当全部数据解析完成之后，持久化到数据库中）
//                        saveStepRedis(appId, json);
                    } catch (Exception e) {
                        AiParseStepVO stepVO = new AiParseStepVO(String.valueOf(step.get()), "AI解析失败", false, appId, e.getMessage());
                        String failJson = JsonUtil.toJson(stepVO);
                        AiInstrumentParseWSServer.send(parseBatchVO.getSessionId(), EnumWebSocketType.AI_PARSE_REALTIME, failJson, EnumMessageType.JSON);
                        log.error(e.getMessage(), e);
//                        throw new BaseException("AI识别异常");
                    }
                    // 添加小延迟，确保数据能够及时发送
                    try {
                        Thread.sleep(10);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("AI解析接口请求失败：", e);
            throw new RuntimeException("AI解析接口请求失败!");
        }
        return step.get();
    }


    /**
     * OCR_AI解析请求
     *
     * @param requestVO AI解析请求参数
     * @return 流式接口结果
     */
    private BufferedReader doStreamRequest(OcrRequestVO requestVO) {
        try {
            String requestUrl = requestVO.getUrl();
            File file = new File(filePath + requestVO.getFilePath());
            RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                    .addFormDataPart("prompt", requestVO.getPrompt())
                    .addFormDataPart("image_file", file.getName(),
                            RequestBody.create(MediaType.parse("application/octet-stream"), file))
                    .build();
            Request request = new Request.Builder()
                    .url(requestUrl)
                    .post(body)
                    .build();
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(30, TimeUnit.SECONDS)
                    .readTimeout(60, TimeUnit.SECONDS)
                    .build();
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new RuntimeException("OCR解析接口请求失败: " + response.code());
            }
            ResponseBody responseBody = response.body();
            assert responseBody != null;
            return new BufferedReader(new InputStreamReader(responseBody.byteStream(),
                    StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("OCR解析接口请求失败：", e);
            throw new RuntimeException("OCR解析接口请求失败!");
        }
    }

    private void saveStepRedis(String appId, String json) {

    }

    private List<AiParseParamDataVO> parseSaveRedis() {

        return null;
    }


    /**
     * 加载冗余字段
     *
     * @param data 应用数据
     */
    private void loadTransientFields(Collection<DtoAiParseFileApp> data) {

        //查询附件数据
        List<String> appIds = data.stream().map(DtoAiParseFileApp::getId).collect(Collectors.toList());
        List<DtoParseDocument> documentList = parseDocumentRepository.findByObjectIdInAndDocTypeId(appIds, EnumDocType.AI_PARSE_APP_FILE.getDocTypeId());
        //按照ObjectId分组附件数据
        Map<String, List<DtoParseDocument>> documentMap = documentList.stream().collect(Collectors.groupingBy(DtoParseDocument::getObjectId));
        //相关枚举Map
        Map<String, String> parseTypeMap = EnumParseType.getMapData();
        Map<String, String> parseStatusMap = EnumAIParseStatus.getMapData();

        //查询关联AI仪器解析配置
        List<String> instrumentIds = data.stream().map(DtoAiParseFileApp::getInstrumentId).collect(Collectors.toList());
        List<DtoAiInstrumentConfig> instrumentConfigList = aiInstrumentConfigRepository.findAll(instrumentIds);
        Map<String, DtoAiInstrumentConfig> instrumentConfigMap = instrumentConfigList.stream().collect(Collectors.toMap(DtoAiInstrumentConfig::getId, dto -> dto));

        //遍历应用数据
        for (DtoAiParseFileApp app : data) {
            //获取应用下的最新的附件
            List<DtoParseDocument> documents = documentMap.getOrDefault(app.getId(), null);
            if (documents != null && !documents.isEmpty()) {
                Optional<DtoParseDocument> docOp = documents.stream().max(Comparator.comparing(DtoParseDocument::getCreateDate));
                docOp.ifPresent(p -> {
                    app.setDocumentId(p.getId());
                    app.setDocumentPath(p.getPath());
                });

            }
            // 设置解析方式名称
            if (app.getParseType() != null) {
                String parseTypeName = parseTypeMap.getOrDefault(app.getParseType(), "");
                app.setParseTypeName(parseTypeName);
            }

            // 设置解析状态名称
            if (app.getParseStatus() != null) {
                String parseStatusName = parseStatusMap.getOrDefault(app.getParseStatus(), "");
                app.setParseStatusName(parseStatusName);
            }

            //设置配置的提示词
            DtoAiInstrumentConfig instrumentConfig = instrumentConfigMap.getOrDefault(app.getInstrumentId(), null);
            if (instrumentConfig != null) {
                app.setPromptText(instrumentConfig.getPromptText());
            }
        }
    }

    @Autowired
    public void setAiInstrumentConfigRepository(AiInstrumentConfigRepository aiInstrumentConfigRepository) {
        this.aiInstrumentConfigRepository = aiInstrumentConfigRepository;
    }

    @Autowired
    public void setParseDocumentRepository(ParseDocumentRepository parseDocumentRepository) {
        this.parseDocumentRepository = parseDocumentRepository;
    }

    @Autowired
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
}
