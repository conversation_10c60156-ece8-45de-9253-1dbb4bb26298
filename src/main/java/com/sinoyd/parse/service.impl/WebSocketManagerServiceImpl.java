package com.sinoyd.parse.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.parse.enums.EnumConnectionStatus;
import com.sinoyd.parse.enums.EnumWebSocketType;
import com.sinoyd.parse.service.WebSocketManagerService;
import com.sinoyd.parse.vo.WSConnectionVO;
import com.sinoyd.parse.vo.WSMessageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.websocket.Session;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * WebSocket管理器服务实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/26
 **/
@Service
@Slf4j
public class WebSocketManagerServiceImpl implements WebSocketManagerService {

    /**
     * 按类型分组的连接集合
     */
    private static final ConcurrentHashMap<EnumWebSocketType, CopyOnWriteArraySet<WSConnectionVO>> connectionsByType = new ConcurrentHashMap<>();

    /**
     * 按用户ID分组的连接集合
     */
    private static final ConcurrentHashMap<String, CopyOnWriteArraySet<WSConnectionVO>> connectionsByUser = new ConcurrentHashMap<>();

    /**
     * 所有连接的映射
     */
    private static final ConcurrentHashMap<String, WSConnectionVO> allConnections = new ConcurrentHashMap<>();

    static {
        // 初始化各种类型的连接集合
        for (EnumWebSocketType type : EnumWebSocketType.values()) {
            connectionsByType.put(type, new CopyOnWriteArraySet<>());
        }
    }

    @Override
    public WSConnectionVO addConnection(Session session, EnumWebSocketType type, String userId) {
        WSConnectionVO connection = new WSConnectionVO(session, type, userId);

        // 设置扩展信息
        if (StringUtil.isNotEmpty(userId)) {
            connection.setUserId(userId);
        }

        // 添加到各个集合中
        connectionsByType.get(type).add(connection);
        allConnections.put(session.getId(), connection);

        // 按用户分组
        if (StringUtil.isNotEmpty(userId)) {
            connectionsByUser.computeIfAbsent(userId, k -> new CopyOnWriteArraySet<>()).add(connection);
        }

        log.info("WebSocket连接建立 - 类型: {}, 会话ID: {}, 用户ID: {}",
                type.getDescription(), session.getId(), userId);

        return connection;
    }

    @Override
    public boolean removeConnection(String sessionId) {
        WSConnectionVO connection = allConnections.remove(sessionId);
        if (connection != null) {
            // 从类型分组中移除
            connectionsByType.get(connection.getType()).remove(connection);

            // 从用户分组中移除
            if (StringUtil.isNotEmpty(connection.getUserId())) {
                CopyOnWriteArraySet<WSConnectionVO> userConnections = connectionsByUser.get(connection.getUserId());
                if (userConnections != null) {
                    userConnections.remove(connection);
                    // 如果用户没有连接了，移除用户记录
                    if (userConnections.isEmpty()) {
                        connectionsByUser.remove(connection.getUserId());
                    }
                }
            }

            // 关闭连接
            connection.close();

            log.info("WebSocket连接关闭 - 类型: {}, 会话ID: {}, 用户ID: {}",
                    connection.getType().getDescription(), sessionId, connection.getUserId());

            return true;
        }
        return false;
    }

    @Override
    public WSConnectionVO getConnection(String sessionId) {
        return allConnections.get(sessionId);
    }

    @Override
    public int sendMessageToType(EnumWebSocketType type, String message) {
        return sendMessageToType(type, WSMessageVO.createTextMessage(type, message));
    }

    @Override
    public int sendMessageToType(EnumWebSocketType type, WSMessageVO wsMessageVO) {
        CopyOnWriteArraySet<WSConnectionVO> connections = connectionsByType.get(type);
        if (connections == null || connections.isEmpty()) {
            return 0;
        }

        int successCount = 0;
        for (WSConnectionVO connection : connections) {
            if (sendMessageToConnection(connection, wsMessageVO)) {
                successCount++;
            }
        }

        log.debug("向类型 {} 发送消息，成功发送 {} 个连接", type.getDescription(), successCount);
        return successCount;
    }

    @Override
    public int sendMessageToType(String sessionId, EnumWebSocketType type, WSMessageVO wsMessageVO) {
        CopyOnWriteArraySet<WSConnectionVO> connections = connectionsByType.get(type);
        if (connections == null || connections.isEmpty()) {
            log.debug("向类型 {} 发送消息，未找到连接", type.getDescription());
            return 0;
        }

        int successCount = 0;
        for (WSConnectionVO connection : connections) {
            if (sessionId != null && sessionId.equals(connection.getSessionId())) {
                if (sendMessageToConnection(connection, wsMessageVO)) {
                    successCount++;
                }
                log.debug("向会话 {} 类型 {} 发送消息", connection.getSessionId(), type.getDescription());
            }
        }
        log.debug("向类型 {} 发送消息，成功发送 {} 个连接", type.getDescription(), successCount);
        return successCount;
    }

    @Override
    public int getConnectionCount(EnumWebSocketType type) {
        CopyOnWriteArraySet<WSConnectionVO> connections = connectionsByType.get(type);
        return connections != null ? connections.size() : 0;
    }

    @Override
    public int getUserConnectionCount(String userId) {
        if (StringUtil.isEmpty(userId)) {
            return 0;
        }
        CopyOnWriteArraySet<WSConnectionVO> userConnections = connectionsByUser.get(userId);
        return userConnections != null ? userConnections.size() : 0;
    }

    @Override
    public int getTotalConnectionCount() {
        return allConnections.size();
    }

    @Override
    public Map<EnumWebSocketType, Integer> getConnectionStatistics() {
        Map<EnumWebSocketType, Integer> statistics = new HashMap<>();
        for (EnumWebSocketType type : EnumWebSocketType.values()) {
            statistics.put(type, getConnectionCount(type));
        }
        return statistics;
    }

    @Override
    public List<WSConnectionVO> getConnectionsByType(EnumWebSocketType type) {
        CopyOnWriteArraySet<WSConnectionVO> connections = connectionsByType.get(type);
        return connections != null ? new ArrayList<>(connections) : new ArrayList<>();
    }

    @Override
    public List<WSConnectionVO> getConnectionsByUser(String userId) {
        if (StringUtil.isEmpty(userId)) {
            return new ArrayList<>();
        }
        CopyOnWriteArraySet<WSConnectionVO> userConnections = connectionsByUser.get(userId);
        return userConnections != null ? new ArrayList<>(userConnections) : new ArrayList<>();
    }

    @Override
    public int cleanInvalidConnections() {
        int cleanedCount = 0;
        List<String> invalidSessionIds = new ArrayList<>();

        // 找出无效连接
        for (WSConnectionVO connection : allConnections.values()) {
            if (!connection.isValid()) {
                invalidSessionIds.add(connection.getSessionId());
            }
        }

        // 移除无效连接
        for (String sessionId : invalidSessionIds) {
            if (removeConnection(sessionId)) {
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            log.info("清理了 {} 个无效的WebSocket连接", cleanedCount);
        }

        return cleanedCount;
    }

    @Override
    public void updateLastActiveTime(String sessionId) {
        WSConnectionVO connection = allConnections.get(sessionId);
        if (connection != null) {
            connection.updateLastActiveTime();
        }
    }

    @Override
    public boolean isConnectionExists(String sessionId) {
        return allConnections.containsKey(sessionId);
    }

    /**
     * 向指定连接发送消息
     *
     * @param connection  WebSocket连接
     * @param wsMessageVO 消息实体
     * @return 是否发送成功
     */
    private boolean sendMessageToConnection(WSConnectionVO connection, WSMessageVO wsMessageVO) {
        if (connection == null || !connection.isValid()) {
            return false;
        }

        try {
            synchronized (connection.getSession()) {
                switch (wsMessageVO.getMessageType()) {
                    case JSON:
                        connection.getSession().getBasicRemote().sendObject(wsMessageVO.getContent());
                        break;
                    case TEXT:
                        connection.getSession().getBasicRemote().sendText(wsMessageVO.getContent());
                        break;
                }
                connection.updateLastActiveTime();
                return true;
            }
        } catch (Exception e) {
            log.error("发送WebSocket消息失败 - 会话ID: {}, 用户ID: {}",
                    connection.getSessionId(), connection.getUserId(), e);
            // 标记连接为异常状态
            connection.setStatus(EnumConnectionStatus.ERROR);
            return false;
        }
    }
}
