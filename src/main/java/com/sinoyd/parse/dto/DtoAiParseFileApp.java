package com.sinoyd.parse.dto;

import com.sinoyd.parse.entity.AiParseFileApp;

import javax.persistence.*;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

/**
 * AI仪器解析应用DTO实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PARSE_AiParseFileApp")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
@ApiModel(description = "AI仪器解析应用DTO实体")
public class DtoAiParseFileApp extends AiParseFileApp {

    private static final long serialVersionUID = 1L;

    /**
     * 解析方式名称
     */
    @Transient
    private String parseTypeName;

    /**
     * 解析状态名称
     */
    @Transient
    private String parseStatusName;

    /**
     * 文件Id
     */
    @Transient
    private String documentId;

    /**
     * 文件路径
     */
    @Transient
    private String documentPath;

    /**
     * 提示词
     */
    @Transient
    private String promptText;
}
