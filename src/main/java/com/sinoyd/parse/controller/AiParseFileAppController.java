package com.sinoyd.parse.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.criteria.AiParseFileAppCriteria;
import com.sinoyd.parse.dto.DtoAiParseFileApp;
import com.sinoyd.parse.service.AiParseFileAppService;
import com.sinoyd.parse.vo.AiParseBatchVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;

/**
 * AI仪器解析应用服务接口定义
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
@Api(tags = "AI仪器解析应用服务")
@RestController
@RequestMapping("api/parse/aiParseFileApp")
@Slf4j
public class AiParseFileAppController extends BaseJpaController<DtoAiParseFileApp, String, AiParseFileAppService> {

    /**
     * 分页动态条件查询AI仪器解析应用
     *
     * @param aiParseFileAppCriteria 条件参数
     * @return 分页数据
     */
    @GetMapping
    @ApiOperation(value = "分页动态条件查询AI仪器解析应用", notes = "分页动态条件查询AI仪器解析应用")
    public RestResponse<List<DtoAiParseFileApp>> findByPage(AiParseFileAppCriteria aiParseFileAppCriteria) {
        RestResponse<List<DtoAiParseFileApp>> restResponse = new RestResponse<>();
        PageBean<DtoAiParseFileApp> pb = super.getPageBean();
        service.findByPage(pb, aiParseFileAppCriteria);
        restResponse.setData(pb.getData());
        restResponse.setCount(pb.getRowsCount());
        restResponse.setRestStatus(StringUtil.isEmpty(pb.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }


    /**
     * 新增AI仪器解析应用
     *
     * @param aiParseFileApp AI仪器解析应用实体
     * @return 新增后的AI仪器解析应用实体
     */
    @ApiOperation(value = "新增AI仪器解析应用", notes = "新增AI仪器解析应用")
    @PostMapping
    public RestResponse<DtoAiParseFileApp> save(@RequestBody DtoAiParseFileApp aiParseFileApp) {
        RestResponse<DtoAiParseFileApp> restResponse = new RestResponse<>();
        DtoAiParseFileApp result = service.save(aiParseFileApp);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(result);
        return restResponse;
    }

    /**
     * 修改AI仪器解析应用
     *
     * @param aiParseFileApp AI仪器解析应用实体
     * @return 更新后的AI仪器解析应用实体
     */
    @ApiOperation(value = "修改AI仪器解析应用", notes = "修改AI仪器解析应用")
    @PutMapping
    public RestResponse<DtoAiParseFileApp> update(@RequestBody DtoAiParseFileApp aiParseFileApp) {
        RestResponse<DtoAiParseFileApp> restResponse = new RestResponse<>();
        DtoAiParseFileApp result = service.update(aiParseFileApp);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(result);
        return restResponse;
    }

    /**
     * 根据ID查询AI仪器解析应用
     *
     * @param id 配置ID
     * @return AI仪器解析应用详情
     */
    @ApiOperation(value = "根据ID查询AI仪器解析应用", notes = "根据ID查询AI仪器解析应用")
    @GetMapping("/{id}")
    public RestResponse<DtoAiParseFileApp> findById(@PathVariable String id) {
        RestResponse<DtoAiParseFileApp> restResponse = new RestResponse<>();
        DtoAiParseFileApp result = service.findOne(id);
        restResponse.setRestStatus(result != null ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        restResponse.setData(result);
        return restResponse;
    }

    /**
     * AI批量解析（WebSocket长连接发送消息）
     *
     * @param parseBatchVO 批量解析参数
     */
    @ApiOperation(value = "AI批量解析（WebSocket长连接）", notes = "AI批量解析（WebSocket长连接）")
    @PostMapping("/batch/ws")
    public RestResponse<Void> batchParseWS(@RequestBody AiParseBatchVO parseBatchVO) {
        service.batchParse(parseBatchVO);
        return new RestResponse<>();
    }

    /**
     * 删除AI仪器解析应用
     *
     * @param ids 应用ID集合
     * @return 删除结果
     */
    @ApiOperation(value = "删除AI仪器解析应用", notes = "删除AI仪器解析应用")
    @DeleteMapping
    public RestResponse<Integer> delete(@RequestBody List<String> ids) {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.logicDeleteById(ids));
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }
}
