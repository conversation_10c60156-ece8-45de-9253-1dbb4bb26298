package com.sinoyd.parse.websocket;

import com.sinoyd.parse.enums.EnumMessageType;
import com.sinoyd.parse.enums.EnumWebSocketType;
import com.sinoyd.parse.service.WebSocketManagerService;
import com.sinoyd.parse.vo.WSMessageVO;
import lombok.extern.slf4j.Slf4j;

/**
 * websockets服务端
 *
 * <AUTHOR>
 * @version V5.2.0
 * @date 2022/4/23
 */
@Slf4j
public abstract class AiInstrumentParseWSServer extends WebSocketConnectServer {

    /**
     * 发送解析完成数据给指定类型的指定会话连接
     *
     * @param sessionId WebSocket会话Id
     * @param type      WebSocket类型
     * @param content   数据内容
     * @return 发送成功的连接数量
     */
    public static int send(String sessionId, EnumWebSocketType type, String content) {
        WSMessageVO message = WSMessageVO.createJsonMessage(type, content);
        WebSocketManagerService service = getWebSocketManagerService();
        if (service != null) {
            return service.sendMessageToType(sessionId, type, message);
        }
        return 0;
    }

    /**
     * 发送解析完成数据给指定类型的指定会话连接
     *
     * @param sessionId WebSocket会话Id
     * @param type      WebSocket类型
     * @param content   数据内容
     * @return 发送成功的连接数量
     */
    public static int send(String sessionId, EnumWebSocketType type, String content, EnumMessageType messageType) {
        WSMessageVO message;
        switch (messageType) {
            case TEXT:
                message = WSMessageVO.createTextMessage(type, content);
                break;
            case JSON:
                message = WSMessageVO.createJsonMessage(type, content);
                break;
            default:
                log.error("不支持的消息类型: {}", messageType);
                return 0;
        }
        WebSocketManagerService service = getWebSocketManagerService();
        if (service != null) {
            return service.sendMessageToType(sessionId, type, message);
        }
        return 0;
    }
}