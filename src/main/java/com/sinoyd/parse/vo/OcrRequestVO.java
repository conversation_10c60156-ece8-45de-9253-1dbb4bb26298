package com.sinoyd.parse.vo;

import lombok.Data;

/**
 * ocr请求VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/27
 */
@Data
public class OcrRequestVO {

    /**
     * ocr请求地址
     */
    private String url;

    /**
     * ocr请求提示词
     */
    private String prompt;

    /**
     * ocr请求文件路径
     */
    private String filePath;

    public OcrRequestVO() {
    }

    public OcrRequestVO(String url, String prompt, String filePath) {
        this.url = url;
        this.prompt = prompt;
        this.filePath = filePath;
    }
}
