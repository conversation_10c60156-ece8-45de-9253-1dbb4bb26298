package com.sinoyd.parse.vo;

import lombok.Data;

/**
 * AI解析步骤VO
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/27
 **/
@Data
public class AiParseStepVO {

    /**
     * 步骤id
     */
    private String stepId;

    /**
     * 步骤名称
     */
    private String stepName;

    /**
     * 是否完成
     */
    private Boolean isFinish;

    /**
     * 对象id
     */
    private String objectId;

    /**
     * 消息
     */
    private String msg;

    /**
     * 默认构造方法
     */
    public AiParseStepVO() {
    }

    /**
     * 构造方法
     */
    public AiParseStepVO(String stepId, String stepName, Boolean isFinish, String objectId, String msg) {
        this();
        this.stepId = stepId;
        this.stepName = stepName;
        this.isFinish = isFinish;
        this.objectId = objectId;
        this.msg = msg;
    }
}
