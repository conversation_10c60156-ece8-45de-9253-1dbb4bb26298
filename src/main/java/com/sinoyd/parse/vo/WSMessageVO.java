package com.sinoyd.parse.vo;

import com.sinoyd.parse.enums.EnumMessageType;
import com.sinoyd.parse.enums.EnumWebSocketType;
import lombok.Data;

/**
 * WebSocket消息实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/26
 **/
@Data
public class WSMessageVO {
    
    /**
     * 消息ID
     */
    private String messageId;
    
    /**
     * 消息类型
     */
    private EnumMessageType messageType;
    
    /**
     * WebSocket连接类型
     */
    private EnumWebSocketType webSocketType;
    
    /**
     * 消息内容
     */
    private String content;

    /**
     * 扩展数据
     */
    private String extendData;
    
    /**
     * 默认构造函数
     */
    public WSMessageVO() {
    }
    
    /**
     * 构造函数
     *
     * @param webSocketType WebSocket连接类型
     * @param messageType   消息类型
     * @param content       消息内容
     */
    public WSMessageVO(EnumWebSocketType webSocketType, EnumMessageType messageType, String content) {
        this();
        this.webSocketType = webSocketType;
        this.messageType = messageType;
        this.content = content;
    }
    
    /**
     * 创建文本消息
     *
     * @param enumWebSocketType WebSocket连接类型
     * @param content       消息内容
     * @return WebSocket消息
     */
    public static WSMessageVO createTextMessage(EnumWebSocketType enumWebSocketType, String content) {
        return new WSMessageVO(enumWebSocketType, EnumMessageType.TEXT, content);
    }

    /**
     * 创建JSON消息
     *
     * @param enumWebSocketType WebSocket连接类型
     * @param content       JSON内容
     * @return WebSocket消息
     */
    public static WSMessageVO createJsonMessage(EnumWebSocketType enumWebSocketType, String content) {
        return new WSMessageVO(enumWebSocketType, EnumMessageType.JSON, content);
    }
}
