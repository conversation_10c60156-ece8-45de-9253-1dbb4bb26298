package com.sinoyd.parse.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消息类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/26
 **/
@Getter
@AllArgsConstructor
public enum EnumMessageType {
    /**
     * 文本消息
     */
    TEXT("text", "文本消息"),

    /**
     * JSON消息
     */
    JSON("json", "JSON消息");

    /**
     * 消息类型代码
     */
    private final String code;

    /**
     * 消息类型描述
     */
    private final String description;
}
