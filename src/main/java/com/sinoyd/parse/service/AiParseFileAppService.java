package com.sinoyd.parse.service;

import com.sinoyd.parse.dto.DtoAiParseFileApp;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.parse.vo.AiParseBatchVO;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

/**
 * AI仪器解析应用操作接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
public interface AiParseFileAppService extends IBaseJpaService<DtoAiParseFileApp, String> {

    /**
     * 批量AI解析（WebSocket长连接）
     *
     * @param parseBatchVO 批量解析参数
     */
    void batchParse(AiParseBatchVO parseBatchVO);

    /**
     * 获取附件上传路径数据
     *
     * @param id 解析应用id
     * @return 解析应用数据
     */
    DtoAiParseFileApp findAttachPath(String id);
}
